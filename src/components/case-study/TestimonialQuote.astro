---
import { Icon } from "astro-icon/components";
import Container from "@/components/container.astro";

interface Props {
  quote: string;
  author: string;
  role: string;
  company?: string;
  avatar?: string;
  companyLogo?: string;
  rating?: number;
  className?: string;
}

const {
  quote,
  author,
  role,
  company,
  avatar,
  companyLogo,
  rating = 5,
  className = ""
} = Astro.props;
---

<section class={`py-16 md:py-20 ${className}`}>
  <Container>
    <div class="max-w-4xl mx-auto">
      <!-- Testimonial Card -->
      <div class="relative bg-gradient-to-br from-[var(--color-brand-50)] to-white rounded-2xl p-8 md:p-12 shadow-lg border border-gray-100">
        <!-- Quote Icon -->
        <div class="absolute top-6 left-6 w-12 h-12 bg-[var(--color-brand)] rounded-full flex items-center justify-center">
          <Icon name="lucide:quote" class="w-6 h-6 text-white" />
        </div>
        
        <!-- Company Logo (if available) -->
        {companyLogo && (
          <div class="absolute top-6 right-6">
            <img 
              src={companyLogo} 
              alt={`${company} logo`}
              class="h-8 w-auto opacity-60"
            />
          </div>
        )}
        
        <!-- Rating Stars -->
        <div class="flex justify-center mb-6 mt-4">
          <div class="flex gap-1">
            {Array.from({ length: 5 }, (_, i) => (
              <Icon 
                name="lucide:star" 
                class={`w-5 h-5 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
              />
            ))}
          </div>
        </div>
        
        <!-- Quote Text -->
        <blockquote class="text-xl md:text-2xl font-medium text-gray-900 text-center leading-relaxed mb-8 italic">
          "{quote}"
        </blockquote>
        
        <!-- Author Info -->
        <div class="flex items-center justify-center gap-4">
          {avatar && (
            <img 
              src={avatar} 
              alt={author}
              class="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
            />
          )}
          
          <div class="text-center">
            <div class="font-semibold text-gray-900">{author}</div>
            <div class="text-gray-600">
              {role}
              {company && (
                <>
                  <span class="text-gray-400 mx-1">•</span>
                  <span class="font-medium">{company}</span>
                </>
              )}
            </div>
          </div>
        </div>
        
        <!-- Decorative Elements -->
        <div class="absolute -bottom-2 -right-2 w-24 h-24 bg-[var(--color-brand-100)] rounded-full opacity-20 blur-xl"></div>
        <div class="absolute -top-2 -left-2 w-16 h-16 bg-[var(--color-brand-100)] rounded-full opacity-30 blur-lg"></div>
      </div>
      
      <!-- Trust Indicators -->
      <div class="mt-8 text-center">
        <div class="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full shadow-sm border border-gray-100">
          <Icon name="lucide:shield-check" class="w-4 h-4 text-green-500" />
          <span class="text-sm font-medium text-gray-700">Testimoni Terverifikasi</span>
        </div>
      </div>
    </div>
  </Container>
</section>

<style>
  /* Subtle animation for the testimonial card */
  .testimonial-card {
    animation: fadeInScale 0.8s ease-out forwards;
    opacity: 0;
    transform: scale(0.95);
  }
  
  @keyframes fadeInScale {
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  /* Hover effect for the card */
  .testimonial-card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
  }
</style>
