---
import { Icon } from "astro-icon/components";
import Container from "@/components/container.astro";

interface Props {
  title?: string;
  client: {
    name: string;
    logo?: string;
    url?: string;
  };
  industry: string;
  location?: string;
  timeline: {
    start: string;
    end: string;
  };
  services: string[];
  techStack: string[];
  className?: string;
}

const {
  title = "Ringkasan Proyek",
  client,
  industry,
  location,
  timeline,
  services,
  techStack,
  className = ""
} = Astro.props;

const facts = [
  {
    icon: "lucide:building-2",
    label: "<PERSON><PERSON><PERSON>",
    value: client.name,
    link: client.url
  },
  {
    icon: "lucide:briefcase",
    label: "Industri",
    value: industry
  },
  ...(location ? [{
    icon: "lucide:map-pin",
    label: "Lokasi",
    value: location
  }] : []),
  {
    icon: "lucide:calendar",
    label: "Timeline",
    value: `${timeline.start} - ${timeline.end}`
  },
  {
    icon: "lucide:settings",
    label: "<PERSON><PERSON><PERSON>",
    value: services.join(", ")
  },
  {
    icon: "lucide:code",
    label: "Teknologi",
    value: techStack.join(", ")
  }
];
---

<section class={`py-16 md:py-20 ${className}`}>
  <Container>
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">{title}</h2>
      <p class="text-lg text-gray-600">Informasi penting tentang proyek ini</p>
    </div>

    <!-- Facts Grid -->
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="grid md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-gray-100">
          {facts.map((fact, index) => (
            <div class="p-6 md:p-8 hover:bg-gray-50 transition-colors">
              <div class="flex items-start gap-4">
                <!-- Icon -->
                <div class="w-12 h-12 rounded-xl bg-[var(--color-brand-50)] flex items-center justify-center flex-shrink-0">
                  <Icon name={fact.icon} class="w-6 h-6 text-[var(--color-brand)]" />
                </div>
                
                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <dt class="text-sm font-medium text-gray-500 mb-1">{fact.label}</dt>
                  <dd class="text-base font-semibold text-gray-900 leading-relaxed">
                    {fact.link ? (
                      <a 
                        href={fact.link} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        class="text-[var(--color-brand)] hover:text-[var(--color-brand-light)] transition-colors inline-flex items-center gap-1"
                      >
                        {fact.value}
                        <Icon name="lucide:external-link" class="w-3 h-3" />
                      </a>
                    ) : (
                      <span>{fact.value}</span>
                    )}
                  </dd>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    <!-- Client Logo Section (if available) -->
    {client.logo && (
      <div class="max-w-4xl mx-auto mt-12">
        <div class="bg-gradient-to-r from-gray-50 to-white rounded-xl p-8 text-center border border-gray-100">
          <p class="text-sm text-gray-600 mb-4">Klien</p>
          <div class="flex justify-center">
            {client.url ? (
              <a 
                href={client.url} 
                target="_blank" 
                rel="noopener noreferrer"
                class="inline-block hover:opacity-80 transition-opacity"
              >
                <img 
                  src={client.logo} 
                  alt={`${client.name} logo`}
                  class="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                />
              </a>
            ) : (
              <img 
                src={client.logo} 
                alt={`${client.name} logo`}
                class="h-12 w-auto"
              />
            )}
          </div>
        </div>
      </div>
    )}
  </Container>
</section>
