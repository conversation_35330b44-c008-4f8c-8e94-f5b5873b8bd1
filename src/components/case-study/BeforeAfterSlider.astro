---
import Container from "@/components/container.astro";

interface BeforeAfter {
  title: string;
  beforeImg: string;
  afterImg: string;
  caption?: string;
}

interface Props {
  title?: string;
  subtitle?: string;
  comparisons: BeforeAfter[];
  className?: string;
}

const {
  title = "Transformasi Visual",
  subtitle = "Lihat perubahan signifikan sebelum dan sesudah optimasi",
  comparisons,
  className = ""
} = Astro.props;
---

<section class={`py-16 md:py-20 ${className}`}>
  <Container>
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">{title}</h2>
      <p class="text-lg text-gray-600">{subtitle}</p>
    </div>

    <!-- Comparisons Grid -->
    <div class="space-y-16">
      {comparisons.map((comparison, index) => (
        <div class="max-w-6xl mx-auto">
          <!-- Comparison Title -->
          <h3 class="text-2xl font-bold text-gray-900 text-center mb-8">{comparison.title}</h3>
          
          <!-- Before/After Container -->
          <div class="relative bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            <!-- Slider Container -->
            <div class="before-after-slider relative" data-index={index}>
              <!-- Before Image -->
              <div class="before-image absolute inset-0">
                <img 
                  src={comparison.beforeImg} 
                  alt={`${comparison.title} - Sebelum`}
                  class="w-full h-full object-cover"
                  loading="lazy"
                />
                <!-- Before Label -->
                <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Sebelum
                </div>
              </div>
              
              <!-- After Image -->
              <div class="after-image absolute inset-0 overflow-hidden" style="clip-path: inset(0 50% 0 0);">
                <img 
                  src={comparison.afterImg} 
                  alt={`${comparison.title} - Sesudah`}
                  class="w-full h-full object-cover"
                  loading="lazy"
                />
                <!-- After Label -->
                <div class="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Sesudah
                </div>
              </div>
              
              <!-- Slider Handle -->
              <div class="slider-handle absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-ew-resize z-10" style="left: 50%; transform: translateX(-50%);">
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                  </svg>
                </div>
              </div>
              
              <!-- Range Input -->
              <input 
                type="range" 
                min="0" 
                max="100" 
                value="50" 
                class="slider-input absolute inset-0 w-full h-full opacity-0 cursor-ew-resize z-20"
                data-slider={index}
              />
            </div>
            
            <!-- Caption -->
            {comparison.caption && (
              <div class="p-6 bg-gray-50 border-t border-gray-100">
                <p class="text-gray-700 text-center">{comparison.caption}</p>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  </Container>
</section>

<style>
  .before-after-slider {
    aspect-ratio: 16 / 9;
    min-height: 400px;
  }
  
  .slider-input::-webkit-slider-thumb {
    appearance: none;
    width: 0;
    height: 0;
  }
  
  .slider-input::-moz-range-thumb {
    width: 0;
    height: 0;
    border: none;
    background: transparent;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const sliders = document.querySelectorAll('.slider-input');
    
    sliders.forEach((slider) => {
      const sliderIndex = slider.getAttribute('data-slider');
      const container = document.querySelector(`[data-index="${sliderIndex}"]`);
      const afterImage = container?.querySelector('.after-image') as HTMLElement;
      const handle = container?.querySelector('.slider-handle') as HTMLElement;
      
      if (!afterImage || !handle) return;
      
      slider.addEventListener('input', (e) => {
        const value = (e.target as HTMLInputElement).value;
        const percentage = parseInt(value);
        
        // Update clip-path for after image
        afterImage.style.clipPath = `inset(0 ${100 - percentage}% 0 0)`;
        
        // Update handle position
        handle.style.left = `${percentage}%`;
      });
      
      // Add touch support for mobile
      let isDragging = false;
      
      const updateSlider = (clientX: number) => {
        if (!container) return;
        
        const rect = container.getBoundingClientRect();
        const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
        
        afterImage.style.clipPath = `inset(0 ${100 - percentage}% 0 0)`;
        handle.style.left = `${percentage}%`;
        (slider as HTMLInputElement).value = percentage.toString();
      };
      
      container?.addEventListener('mousedown', (e) => {
        isDragging = true;
        updateSlider(e.clientX);
      });
      
      document.addEventListener('mousemove', (e) => {
        if (isDragging) {
          updateSlider(e.clientX);
        }
      });
      
      document.addEventListener('mouseup', () => {
        isDragging = false;
      });
      
      // Touch events
      container?.addEventListener('touchstart', (e) => {
        isDragging = true;
        updateSlider(e.touches[0].clientX);
      });
      
      document.addEventListener('touchmove', (e) => {
        if (isDragging) {
          e.preventDefault();
          updateSlider(e.touches[0].clientX);
        }
      });
      
      document.addEventListener('touchend', () => {
        isDragging = false;
      });
    });
  });
</script>
