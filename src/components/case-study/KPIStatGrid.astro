---
import { Icon } from "astro-icon/components";
import Container from "@/components/container.astro";

interface KPI {
  label: string;
  before: string | number;
  after: string | number;
  unit?: string;
  improvement?: string;
}

interface Props {
  title?: string;
  subtitle?: string;
  kpis: KPI[];
  className?: string;
}

const { 
  title = "Hasil yang Dicapai",
  subtitle = "Peningkatan performa yang terukur dan signifikan",
  kpis,
  className = ""
} = Astro.props;

// Helper function to format KPI values
function formatKPIValue(value: string | number, unit?: string): string {
  if (typeof value === 'number') {
    return unit ? `${value}${unit}` : value.toString();
  }
  return unit ? `${value}${unit}` : value;
}

// Helper function to calculate improvement percentage
function calculateImprovement(before: string | number, after: string | number): string {
  const beforeNum = typeof before === 'number' ? before : parseFloat(before.toString());
  const afterNum = typeof after === 'number' ? after : parseFloat(after.toString());
  
  if (isNaN(beforeNum) || isNaN(afterNum) || beforeNum === 0) {
    return '';
  }
  
  const improvement = ((afterNum - beforeNum) / beforeNum) * 100;
  return improvement > 0 ? `+${improvement.toFixed(1)}%` : `${improvement.toFixed(1)}%`;
}
---

<section class={`py-16 md:py-20 relative overflow-hidden ${className}`}>
  <!-- Background with subtle gradient -->
  <div class="absolute inset-0 bg-gradient-to-br from-[var(--color-brand-50)] to-white"></div>
  <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-[var(--color-brand)] to-transparent opacity-30"></div>
  
  <Container>
    <div class="relative z-10">
      <!-- Section Header -->
      <div class="max-w-3xl mx-auto text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">{title}</h2>
        <p class="text-lg text-gray-600">{subtitle}</p>
      </div>
      
      <!-- KPI Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
        {kpis.map((kpi, index) => {
          const improvement = kpi.improvement || calculateImprovement(kpi.before, kpi.after);
          
          return (
            <div class="kpi-card group relative">
              <!-- Card -->
              <div class="relative bg-white rounded-2xl p-6 md:p-8 text-center shadow-sm border border-gray-100 transition-all duration-500 hover:shadow-lg hover:border-[var(--color-brand)]/20 h-full flex flex-col justify-center">
                <!-- Glowing border effect on hover -->
                <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-[var(--color-brand)] to-blue-400 opacity-0 group-hover:opacity-10 transition-opacity duration-500 pointer-events-none"></div>
                
                <!-- Before/After Values -->
                <div class="mb-4">
                  <div class="text-sm text-gray-500 mb-2">Sebelum → Sesudah</div>
                  <div class="flex items-center justify-center gap-2 text-lg font-semibold">
                    <span class="text-gray-600">{formatKPIValue(kpi.before, kpi.unit)}</span>
                    <Icon name="lucide:arrow-right" class="w-4 h-4 text-[var(--color-brand)]" />
                    <span class="text-[var(--color-brand)]">{formatKPIValue(kpi.after, kpi.unit)}</span>
                  </div>
                </div>
                
                <!-- Improvement Badge -->
                {improvement && (
                  <div class="mb-4">
                    <span class={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${
                      improvement.startsWith('+') 
                        ? 'bg-green-100 text-green-700' 
                        : 'bg-red-100 text-red-700'
                    }`}>
                      <Icon 
                        name={improvement.startsWith('+') ? 'lucide:trending-up' : 'lucide:trending-down'} 
                        class="w-3 h-3" 
                      />
                      {improvement}
                    </span>
                  </div>
                )}
                
                <!-- Label -->
                <h3 class="text-base md:text-lg font-semibold text-gray-900 leading-tight">{kpi.label}</h3>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  </Container>
</section>

<style>
  .kpi-card {
    animation: fadeInUp 0.6s ease-out forwards;
    animation-delay: calc(var(--index, 0) * 0.1s);
    opacity: 0;
    transform: translateY(20px);
  }
  
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>

<script>
  // Add staggered animation delay to each card
  document.addEventListener('DOMContentLoaded', () => {
    const cards = document.querySelectorAll('.kpi-card');
    cards.forEach((card, index) => {
      (card as HTMLElement).style.setProperty('--index', index.toString());
    });
  });
</script>
