---
import Container from "@/components/container.astro";

interface MediaItem {
  src: string;
  alt: string;
  caption?: string;
  type?: 'image' | 'video';
}

interface Props {
  title?: string;
  subtitle?: string;
  media: MediaItem[];
  layout?: 'grid' | 'carousel' | 'masonry';
  className?: string;
}

const {
  title,
  subtitle,
  media,
  layout = 'grid',
  className = ""
} = Astro.props;
---

<section class={`py-16 md:py-20 ${className}`}>
  <Container>
    {(title || subtitle) && (
      <!-- Section Header -->
      <div class="max-w-3xl mx-auto text-center mb-12">
        {title && <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">{title}</h2>}
        {subtitle && <p class="text-lg text-gray-600">{subtitle}</p>}
      </div>
    )}

    <!-- Media Grid -->
    {layout === 'grid' && (
      <div class={`grid gap-6 md:gap-8 ${
        media.length === 1 ? 'grid-cols-1' :
        media.length === 2 ? 'grid-cols-1 md:grid-cols-2' :
        media.length === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
        'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      }`}>
        {media.map((item, index) => (
          <figure class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            {item.type === 'video' ? (
              <video 
                src={item.src}
                controls
                class="w-full aspect-video object-cover"
                poster={item.alt}
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <img 
                src={item.src} 
                alt={item.alt}
                class="w-full aspect-video object-cover hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            )}
            
            {item.caption && (
              <figcaption class="p-4 bg-gray-50 border-t border-gray-100">
                <p class="text-sm text-gray-700 text-center">{item.caption}</p>
              </figcaption>
            )}
          </figure>
        ))}
      </div>
    )}

    <!-- Masonry Layout -->
    {layout === 'masonry' && (
      <div class="columns-1 md:columns-2 lg:columns-3 gap-6 md:gap-8">
        {media.map((item, index) => (
          <figure class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1 mb-6 md:mb-8 break-inside-avoid">
            {item.type === 'video' ? (
              <video 
                src={item.src}
                controls
                class="w-full object-cover"
                poster={item.alt}
              >
                Your browser does not support the video tag.
              </video>
            ) : (
              <img 
                src={item.src} 
                alt={item.alt}
                class="w-full object-cover hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            )}
            
            {item.caption && (
              <figcaption class="p-4 bg-gray-50 border-t border-gray-100">
                <p class="text-sm text-gray-700 text-center">{item.caption}</p>
              </figcaption>
            )}
          </figure>
        ))}
      </div>
    )}

    <!-- Carousel Layout -->
    {layout === 'carousel' && (
      <div class="relative">
        <div class="carousel-container overflow-hidden rounded-xl">
          <div class="carousel-track flex transition-transform duration-300 ease-in-out" id="carousel-track">
            {media.map((item, index) => (
              <div class="carousel-slide flex-shrink-0 w-full">
                <figure class="bg-white shadow-md border border-gray-100 overflow-hidden">
                  {item.type === 'video' ? (
                    <video 
                      src={item.src}
                      controls
                      class="w-full aspect-video object-cover"
                      poster={item.alt}
                    >
                      Your browser does not support the video tag.
                    </video>
                  ) : (
                    <img 
                      src={item.src} 
                      alt={item.alt}
                      class="w-full aspect-video object-cover"
                      loading="lazy"
                    />
                  )}
                  
                  {item.caption && (
                    <figcaption class="p-4 bg-gray-50 border-t border-gray-100">
                      <p class="text-sm text-gray-700 text-center">{item.caption}</p>
                    </figcaption>
                  )}
                </figure>
              </div>
            ))}
          </div>
        </div>
        
        <!-- Carousel Controls -->
        {media.length > 1 && (
          <div class="flex justify-center mt-6 gap-2">
            {media.map((_, index) => (
              <button 
                class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-[var(--color-brand)] transition-colors"
                data-slide={index}
              ></button>
            ))}
          </div>
        )}
      </div>
    )}
  </Container>
</section>

{layout === 'carousel' && (
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const track = document.getElementById('carousel-track');
      const dots = document.querySelectorAll('.carousel-dot');
      let currentSlide = 0;
      
      if (!track || dots.length === 0) return;
      
      // Set initial active dot
      dots[0]?.classList.add('!bg-[var(--color-brand)]');
      
      dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
          currentSlide = index;
          updateCarousel();
        });
      });
      
      function updateCarousel() {
        const translateX = -currentSlide * 100;
        track.style.transform = `translateX(${translateX}%)`;
        
        // Update dots
        dots.forEach((dot, index) => {
          if (index === currentSlide) {
            dot.classList.add('!bg-[var(--color-brand)]');
          } else {
            dot.classList.remove('!bg-[var(--color-brand)]');
          }
        });
      }
      
      // Auto-advance carousel (optional)
      setInterval(() => {
        currentSlide = (currentSlide + 1) % dots.length;
        updateCarousel();
      }, 5000);
    });
  </script>
)}

<style>
  .carousel-dot.active {
    background-color: var(--color-brand);
  }
</style>
