---
import { Icon } from "astro-icon/components";
import Container from "@/components/container.astro";

interface TimelineItem {
  title: string;
  description: string;
  date?: string;
  icon?: string;
  status?: 'completed' | 'current' | 'upcoming';
}

interface Props {
  title?: string;
  subtitle?: string;
  items: TimelineItem[];
  className?: string;
}

const {
  title = "Proses Pengerjaan",
  subtitle = "Timeline dan milestone utama dalam proyek ini",
  items,
  className = ""
} = Astro.props;
---

<section class={`py-16 md:py-20 ${className}`}>
  <Container>
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">{title}</h2>
      <p class="text-lg text-gray-600">{subtitle}</p>
    </div>

    <!-- Timeline -->
    <div class="max-w-4xl mx-auto">
      <div class="relative">
        <!-- Timeline Line -->
        <div class="absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 transform md:-translate-x-0.5"></div>
        
        <!-- Timeline Items -->
        <div class="space-y-8 md:space-y-12">
          {items.map((item, index) => {
            const isEven = index % 2 === 0;
            const statusColors = {
              completed: 'bg-green-500 border-green-200',
              current: 'bg-[var(--color-brand)] border-blue-200',
              upcoming: 'bg-gray-300 border-gray-200'
            };
            const statusColor = statusColors[item.status || 'completed'];
            
            return (
              <div class={`relative flex items-center ${isEven ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                <!-- Timeline Dot -->
                <div class={`absolute left-8 md:left-1/2 w-4 h-4 rounded-full border-4 ${statusColor} transform md:-translate-x-1/2 z-10`}>
                  {item.icon && (
                    <div class="absolute inset-0 flex items-center justify-center">
                      <Icon name={item.icon} class="w-2 h-2 text-white" />
                    </div>
                  )}
                </div>
                
                <!-- Content Card -->
                <div class={`ml-20 md:ml-0 md:w-1/2 ${isEven ? 'md:pr-8' : 'md:pl-8'}`}>
                  <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <!-- Date Badge -->
                    {item.date && (
                      <div class="inline-flex items-center gap-2 px-3 py-1 bg-[var(--color-brand-50)] rounded-full text-sm font-medium text-[var(--color-brand)] mb-3">
                        <Icon name="lucide:calendar" class="w-3 h-3" />
                        {item.date}
                      </div>
                    )}
                    
                    <!-- Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    
                    <!-- Description -->
                    <p class="text-gray-600 leading-relaxed">{item.description}</p>
                    
                    <!-- Status Indicator -->
                    {item.status && (
                      <div class="mt-4 flex items-center gap-2">
                        <div class={`w-2 h-2 rounded-full ${
                          item.status === 'completed' ? 'bg-green-500' :
                          item.status === 'current' ? 'bg-[var(--color-brand)]' :
                          'bg-gray-300'
                        }`}></div>
                        <span class={`text-sm font-medium ${
                          item.status === 'completed' ? 'text-green-700' :
                          item.status === 'current' ? 'text-[var(--color-brand)]' :
                          'text-gray-500'
                        }`}>
                          {item.status === 'completed' ? 'Selesai' :
                           item.status === 'current' ? 'Sedang Berlangsung' :
                           'Akan Datang'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  </Container>
</section>

<style>
  /* Animation for timeline items */
  .timeline-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .timeline-item:nth-child(1) { animation-delay: 0.1s; }
  .timeline-item:nth-child(2) { animation-delay: 0.2s; }
  .timeline-item:nth-child(3) { animation-delay: 0.3s; }
  .timeline-item:nth-child(4) { animation-delay: 0.4s; }
  .timeline-item:nth-child(5) { animation-delay: 0.5s; }
  .timeline-item:nth-child(6) { animation-delay: 0.6s; }
  
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .timeline-item {
      margin-left: 0;
    }
  }
</style>
