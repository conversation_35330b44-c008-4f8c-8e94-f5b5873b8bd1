---
import { Icon } from "astro-icon/components";
import Button from "@/components/ui/button.astro";
import Container from "@/components/container.astro";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

interface KPI {
  label: string;
  before: string | number;
  after: string | number;
  unit?: string;
  improvement?: string;
}

interface Props {
  title: string;
  subtitle: string;
  clientName: string;
  clientLogo?: string;
  industry: string;
  timeline: string;
  kpis: KPI[];
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
}

const {
  title,
  subtitle,
  clientName,
  clientLogo,
  industry,
  timeline,
  kpis,
  primaryButtonText = "Konsultasi Gratis",
  primaryButtonLink = "https://wa.me/6281291274023",
  secondaryButtonText = "Lihat Layanan",
  secondaryButtonLink = "/jasa"
} = Astro.props;

// Take only the first 4 KPIs for hero display
const heroKPIs = kpis.slice(0, 4);
---

<main class="relative overflow-hidden hero-main-services">
  <!-- Background design elements -->
  <BackgroundElements />
  <div class="absolute inset-0 -z-10 overflow-hidden bg-gradient-to-b from-[var(--color-brand-50)] to-white">
    <div class="absolute -right-10 -top-20 h-64 w-64 rounded-full bg-[var(--color-brand-100)] opacity-20 blur-3xl"></div>
    <div class="absolute -left-20 top-40 h-72 w-72 rounded-full bg-[var(--color-brand-100)] opacity-10 blur-3xl"></div>
  </div>
  
  <Container>
    <div class="relative z-10 max-w-6xl mx-auto">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        
        <!-- Left Column: Content -->
        <div class="text-center lg:text-left">
          <!-- Client Badge -->
          <div class="inline-flex items-center gap-3 bg-white rounded-full px-4 py-2 shadow-sm border border-gray-100 mb-6">
            {clientLogo && (
              <img 
                src={clientLogo} 
                alt={`${clientName} logo`}
                class="h-6 w-auto"
              />
            )}
            <span class="text-sm font-medium text-gray-700">{clientName}</span>
            <span class="text-xs text-gray-500">•</span>
            <span class="text-xs text-gray-500">{industry}</span>
          </div>

          <!-- Title -->
          <h1 class="text-4xl lg:text-5xl font-bold lg:tracking-tight mb-6 leading-tight">
            {title}
          </h1>
          
          <!-- Subtitle -->
          <p class="text-lg lg:text-xl text-gray-700 mb-8 leading-relaxed">
            {subtitle}
          </p>

          <!-- Timeline -->
          <div class="flex items-center justify-center lg:justify-start gap-2 text-sm text-gray-600 mb-8">
            <Icon name="lucide:calendar" class="w-4 h-4" />
            <span>{timeline}</span>
          </div>
          
          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
            <a href={primaryButtonLink} rel="noopener noreferrer" target="_blank">
              <Button size="lg" class="flex gap-2 items-center justify-center w-full sm:w-auto px-8 py-3">
                <Icon name="lucide:message-circle" class="w-4 h-4" />
                {primaryButtonText}
              </Button>
            </a>
            <a href={secondaryButtonLink} rel="noopener">
              <Button size="lg" style="neutral" class="flex gap-2 items-center justify-center w-full sm:w-auto px-8 py-3">
                <Icon name="lucide:arrow-right" class="w-4 h-4" />
                {secondaryButtonText}
              </Button>
            </a>
          </div>
        </div>

        <!-- Right Column: KPI Pills -->
        <div class="lg:pl-8">
          <div class="grid grid-cols-2 gap-4">
            {heroKPIs.map((kpi, index) => (
              <div class="bg-white rounded-xl p-6 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <!-- KPI Value -->
                <div class="text-center mb-3">
                  <div class="text-2xl lg:text-3xl font-bold text-[var(--color-brand)] mb-1">
                    {typeof kpi.after === 'number' ? kpi.after : kpi.after}
                    {kpi.unit && <span class="text-lg text-gray-600">{kpi.unit}</span>}
                  </div>
                  {kpi.improvement && (
                    <div class={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      kpi.improvement.startsWith('+') 
                        ? 'bg-green-100 text-green-700' 
                        : 'bg-red-100 text-red-700'
                    }`}>
                      <Icon 
                        name={kpi.improvement.startsWith('+') ? 'lucide:trending-up' : 'lucide:trending-down'} 
                        class="w-3 h-3" 
                      />
                      {kpi.improvement}
                    </div>
                  )}
                </div>
                
                <!-- KPI Label -->
                <div class="text-center">
                  <div class="text-sm font-medium text-gray-900 leading-tight">{kpi.label}</div>
                  <div class="text-xs text-gray-500 mt-1">
                    {typeof kpi.before === 'number' ? kpi.before : kpi.before}{kpi.unit} → {typeof kpi.after === 'number' ? kpi.after : kpi.after}{kpi.unit}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </Container>
</main>
