---
import { Icon } from "astro-icon/components";

interface Metric {
  value: string;
  label: string;
  description: string;
  icon: string;
}

interface Props {
  title?: string;
  subtitle?: string;
  metrics: Metric[];
}

const { 
  title = "Mengapa Memilih <PERSON>?",
  subtitle = "Komitmen kami untuk memberikan layanan migrasi terbaik dengan jaminan kualitas",
  metrics 
} = Astro.props;
---

<section class="py-16 md:py-20 relative overflow-hidden">
  <!-- Background with gradient -->
  <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white"></div>
  <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-[var(--color-brand)] to-transparent opacity-30"></div>
  
  <div class="max-w-7xl mx-auto px-4 relative z-10">
    <!-- Section Header -->
    <div class="max-w-3xl mx-auto text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900">{title}</h2>
      <p class="text-lg text-gray-600">{subtitle}</p>
    </div>
    
    <!-- Metrics Grid -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
      {metrics.map((metric, index) => (
        <div class="metric-card group relative">
          <!-- Card -->
          <div class="relative bg-white rounded-2xl p-6 md:p-8 text-center shadow-sm border border-gray-100 transition-all duration-500 hover:shadow-lg hover:border-[var(--color-brand)]/20 h-full flex flex-col justify-center">
            <!-- Glowing border effect on hover -->
            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-[var(--color-brand)] to-yellow-400 opacity-0 group-hover:opacity-10 transition-opacity duration-500 pointer-events-none"></div>
            
            <!-- Icon -->
            <div class="w-16 h-16 mx-auto mb-4 rounded-xl bg-gradient-to-br from-[var(--color-brand)] to-yellow-400 flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
              <Icon name={metric.icon} class="w-8 h-8 text-white" />
            </div>
            
            <!-- Value -->
            <div class="mb-2">
              <span class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[var(--color-brand)] to-yellow-500 bg-clip-text text-transparent">
                {metric.value}
              </span>
            </div>
            
            <!-- Label -->
            <h3 class="text-lg md:text-xl font-semibold text-gray-900 mb-2">{metric.label}</h3>
            
            <!-- Description -->
            <p class="text-sm text-gray-600 leading-relaxed">{metric.description}</p>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>

<style>
  .metric-card::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -10;
    border-radius: 1rem;
    background: linear-gradient(135deg, var(--color-brand), #f59e0b);
    opacity: 0;
    transition: opacity 0.5s ease;
    padding: 2px;
  }
  
  .metric-card:hover::before {
    opacity: 0.15;
  }
  
  .metric-card::after {
    content: '';
    position: absolute;
    inset: 2px;
    z-index: -5;
    border-radius: calc(1rem - 2px);
    background: white;
    opacity: 0;
    transition: opacity 0.5s ease;
  }
  
  .metric-card:hover::after {
    opacity: 1;
  }
</style>