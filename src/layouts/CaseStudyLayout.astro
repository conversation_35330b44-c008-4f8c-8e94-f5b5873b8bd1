---
import Container from "@/components/container.astro";
import { getFormattedDate } from "@/utils/all";
import Layout from "./Layout.astro";
import { Icon } from "astro-icon/components";

const { frontmatter } = Astro.props;

// Ensure description is not undefined and not empty string
const description = frontmatter.description && frontmatter.description.trim() !== '' 
  ? frontmatter.description 
  : `Studi kasus ${frontmatter.title} oleh ${frontmatter.author}`;

// JSON-LD structured data for case study
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "CaseStudy",
  "name": frontmatter.title,
  "headline": frontmatter.title,
  "description": description,
  "abstract": frontmatter.snippet || description,
  "provider": {
    "@type": "Organization",
    "name": "Harun Studio",
    "url": "https://harunstudio.com",
    "logo": "https://harunstudio.com/favicon.svg",
    "sameAs": [
      "https://www.linkedin.com/company/harunstudio",
      "https://github.com/randyhs123"
    ]
  },
  "about": {
    "@type": "Organization",
    "name": frontmatter.client.name,
    "url": frontmatter.client.url,
    "industry": frontmatter.industry
  },
  "datePublished": frontmatter.publishDate.toISOString(),
  "dateModified": frontmatter.publishDate.toISOString(),
  "author": {
    "@type": "Person",
    "name": frontmatter.author,
    "jobTitle": "Web Developer & SEO Specialist",
    "worksFor": {
      "@type": "Organization",
      "name": "Harun Studio"
    }
  },
  "publisher": {
    "@type": "Organization",
    "name": "Harun Studio",
    "url": "https://harunstudio.com",
    "logo": {
      "@type": "ImageObject",
      "url": "https://harunstudio.com/favicon.svg"
    }
  },
  "image": {
    "@type": "ImageObject",
    "url": frontmatter.image.src,
    "description": frontmatter.image.alt
  },
  "url": `https://harunstudio.com/studi-kasus/${Astro.params.slug}`,
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": `https://harunstudio.com/studi-kasus/${Astro.params.slug}`
  },
  "keywords": frontmatter.tags.join(", "),
  "articleSection": "Case Study",
  "inLanguage": "id-ID",
  // Add service offerings mentioned in the case study
  "mentions": frontmatter.services.map(service => ({
    "@type": "Service",
    "name": service,
    "provider": {
      "@type": "Organization",
      "name": "Harun Studio"
    }
  })),
  // Add technology stack as software applications
  "softwareApplication": frontmatter.techStack.map(tech => ({
    "@type": "SoftwareApplication",
    "name": tech
  }))
};
---

<Layout 
  title={frontmatter.title} 
  description={description}
  ogImage={frontmatter.image.src}
  ogImageAlt={frontmatter.image.alt}
  ogType="article"
  jsonLd={jsonLd}
>
  <!-- Case Study Header -->
  <div class="relative pt-32 pb-10 md:pt-40 md:pb-16">
    <Container>
      <div class="mx-auto max-w-4xl">
        <!-- Breadcrumb -->
        <nav class="flex items-center gap-2 text-sm text-gray-500 mb-6">
          <a href="/" class="hover:text-[var(--color-brand)] transition-colors">Home</a>
          <Icon name="lucide:chevron-right" class="w-4 h-4" />
          <a href="/studi-kasus" class="hover:text-[var(--color-brand)] transition-colors">Studi Kasus</a>
          <Icon name="lucide:chevron-right" class="w-4 h-4" />
          <span class="text-gray-700">Saat ini</span>
        </nav>

        <!-- Category and Date -->
        <div class="flex items-center gap-3 mb-6">
          <span class="px-3 py-1 bg-[var(--color-brand-50)] rounded-full text-sm font-medium text-[var(--color-brand)]">
            STUDI KASUS
          </span>
          <span class="text-gray-500 text-sm">
            {frontmatter.industry}
          </span>
          <span class="text-gray-400">•</span>
          <time class="text-gray-500 text-sm" datetime={frontmatter.publishDate.toISOString()}>
            {getFormattedDate(frontmatter.publishDate)}
          </time>
        </div>
        
        <!-- Title -->
        <h1 class="text-4xl lg:text-5xl font-bold lg:tracking-tight mt-1 lg:leading-tight mb-6">
          {frontmatter.title}
        </h1>

        <!-- Client Info -->
        <div class="flex items-center gap-4 mb-8">
          {frontmatter.client.logo && (
            <img 
              src={frontmatter.client.logo} 
              alt={`${frontmatter.client.name} logo`}
              class="h-8 w-auto"
            />
          )}
          <div>
            <div class="font-semibold text-gray-900">{frontmatter.client.name}</div>
            {frontmatter.location && (
              <div class="text-sm text-gray-600">{frontmatter.location}</div>
            )}
          </div>
        </div>

        <!-- Author Info -->
        <div class="flex items-center border-b border-gray-100 pb-6">
          <span class="font-medium text-gray-900">{frontmatter.author}</span>
          <span class="mx-2 text-gray-400">|</span>
          <span class="text-gray-600 text-sm">
            {frontmatter.timeline.start} - {frontmatter.timeline.end}
          </span>
        </div>
      </div>
    </Container>
  </div>

  <!-- Case Study Content -->
  <Container>
    <div class="mx-auto max-w-4xl">
      <!-- Featured Image -->
      <div class="rounded-xl overflow-hidden mb-12 shadow-lg">
        <img
          src={frontmatter.image.src}
          alt={frontmatter.image.alt}
          class="w-full object-cover"
          loading="eager"
        />
      </div>

      <!-- Main Content -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-12">
        <div class="p-8 md:p-12">
          <div class="prose prose-lg prose-headings:font-bold prose-headings:text-gray-900 prose-p:text-gray-700 prose-img:rounded-xl prose-a:text-[var(--color-brand)] max-w-none">
            <slot />
          </div>
        </div>
      </div>
      
      <!-- Tags -->
      <div class="border-t border-gray-100 pt-8 flex flex-wrap gap-3 mb-16">
        {frontmatter.tags.map((tag) => (
          <span class="px-3 py-1 bg-gray-100 rounded-full text-sm font-medium text-gray-700 hover:bg-[var(--color-brand-50)] hover:text-[var(--color-brand)] transition-colors">
            #{tag}
          </span>
        ))}
      </div>

      <!-- Related Services CTA -->
      {frontmatter.relatedServices && frontmatter.relatedServices.length > 0 && (
        <div class="bg-gradient-to-br from-[var(--color-brand-50)] to-white rounded-xl p-8 md:p-12 text-center mb-16">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">
            Tertarik dengan Layanan Serupa?
          </h3>
          <p class="text-gray-600 mb-8 max-w-2xl mx-auto">
            Kami siap membantu transformasi digital bisnis Anda dengan layanan yang telah terbukti memberikan hasil optimal.
          </p>
          <div class="flex flex-wrap justify-center gap-4">
            {frontmatter.relatedServices.map((service) => (
              <a 
                href={`/jasa/${service.slug}`}
                class="inline-flex items-center gap-2 px-6 py-3 bg-[var(--color-brand)] text-white rounded-lg hover:bg-[var(--color-brand-light)] transition-colors font-medium"
              >
                <Icon name="lucide:arrow-right" class="w-4 h-4" />
                {service.title}
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  </Container>
</Layout>
