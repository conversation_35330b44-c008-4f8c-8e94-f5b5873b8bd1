---
import { getCollection } from "astro:content";
import CaseStudyLayout from "@/layouts/CaseStudyLayout.astro";

// Generate a new path for every case study entry
export async function getStaticPaths() {
  const caseStudyEntries = await getCollection("case-studies");
  return caseStudyEntries.map((entry) => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

// Get the entry directly from the prop on render
const { entry } = Astro.props;
const { Content } = await entry.render();
---

<CaseStudyLayout frontmatter={entry.data}>
  <Content />
</CaseStudyLayout>
