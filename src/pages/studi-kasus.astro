---
import { getCollection } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Container from "@/components/container.astro";
import { Icon } from "astro-icon/components";
import BackgroundElements from "@/components/ui/BackgroundElements.astro";

const metaDescription = "Studi kasus Harun Studio: Lihat transformasi digital nyata klien kami dengan peningkatan performa, optimasi SEO, dan migrasi teknologi modern.";

// Filter case study entries with 'draft: false' & date before current date
const publishedCaseStudies = await getCollection("case-studies", ({ data }) => {
  return !data.draft && data.publishDate < new Date();
});

// Sort content entries by publication date
publishedCaseStudies.sort(function (a, b) {
  return b.data.publishDate.valueOf() - a.data.publishDate.valueOf();
});


---

<Layout title="Studi Kasus" description={metaDescription}>
  <!-- Background design elements -->
  <BackgroundElements />
  <div class="relative pt-32 pb-5 md:pt-40 md:pb-10">
    <Container>
      <div class="flex flex-col items-center text-center">
        <h1 class="mb-4">
          Studi Kasus
        </h1>
        <p class="text-lg max-w-2xl text-gray-600">
          Transformasi digital nyata yang telah kami lakukan untuk klien. Lihat bagaimana kami meningkatkan performa, optimasi SEO, dan migrasi teknologi modern.
        </p>
      </div>
    </Container>
  </div>

  <Container>
    {publishedCaseStudies.length > 0 ? (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 mt-10 mb-20">
        {publishedCaseStudies.map((caseStudy) => (
          <div class="bg-white rounded-xl shadow-sm overflow-hidden flex flex-col h-full hover:shadow-md transition-shadow border border-gray-100">
            <a href={`/studi-kasus/${caseStudy.slug}`} class="block overflow-hidden">
              <div class="aspect-video overflow-hidden">
                <img
                  src={caseStudy.data.image.src}
                  alt={caseStudy.data.image.alt}
                  class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                  loading="lazy"
                />
              </div>
            </a>

            <div class="p-6 flex flex-col flex-1">
              <!-- Category and Client -->
              <div class="flex items-center gap-2 mb-3">
                <span class="px-3 py-1 bg-[var(--color-brand-50)] rounded-full text-xs font-medium text-[var(--color-brand)]">
                  STUDI KASUS
                </span>
                <span class="text-sm text-gray-500">
                  {caseStudy.data.client.name}
                </span>
              </div>

              <!-- Title -->
              <a href={`/studi-kasus/${caseStudy.slug}`} class="group">
                <h2 class="text-xl font-semibold leading-snug tracking-tight mb-3 group-hover:text-[var(--color-brand)] transition-colors">
                  {caseStudy.data.title}
                </h2>
              </a>

              <!-- Description -->
              <p class="text-gray-600 mb-4 flex-1">
                {caseStudy.data.snippet}
              </p>

              <!-- KPI Preview -->
              {caseStudy.data.kpis && caseStudy.data.kpis.length > 0 && (
                <div class="grid grid-cols-2 gap-3 mb-4 p-3 bg-gray-50 rounded-lg">
                  {caseStudy.data.kpis.slice(0, 2).map((kpi) => (
                    <div class="text-center">
                      <div class="text-sm font-bold text-[var(--color-brand)]">
                        {typeof kpi.after === 'number' ? kpi.after : kpi.after}{kpi.unit}
                      </div>
                      <div class="text-xs text-gray-600">{kpi.label}</div>
                    </div>
                  ))}
                </div>
              )}

              <!-- Meta Info -->
              <div class="flex items-center justify-between text-sm text-gray-500 pt-3 border-t border-gray-100">
                <span>{caseStudy.data.industry}</span>
                <span>
                  {new Date(caseStudy.data.publishDate).toLocaleDateString('id-ID', {
                    year: 'numeric',
                    month: 'short'
                  })}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    ) : (
      <!-- Empty State -->
      <div class="text-center py-20">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <Icon name="lucide:file-text" class="w-12 h-12 text-gray-400" />
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Studi Kasus</h3>
        <p class="text-gray-600 mb-8">Studi kasus akan segera tersedia. Pantau terus untuk melihat transformasi digital terbaru.</p>
        <a
          href="/jasa"
          class="inline-flex items-center gap-2 px-6 py-3 bg-[var(--color-brand)] text-white rounded-lg hover:bg-[var(--color-brand-light)] transition-colors font-medium"
        >
          <Icon name="lucide:arrow-right" class="w-4 h-4" />
          Lihat Layanan Kami
        </a>
      </div>
    )}
  </Container>
</Layout>
