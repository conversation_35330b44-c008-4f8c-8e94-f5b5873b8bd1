---
import Layout from "@/layouts/Layout.astro";
import HeroSection from "@/components/services/HeroSection.astro";
import BusinessFocusSection from "@/components/services/BusinessFocusSection.astro";
import BenefitsSection from "@/components/services/BenefitsSection.astro";
import ProcessSection from "@/components/services/ProcessSection.astro";
import FaqSection from "@/components/services/FaqSection.astro";
import CtaSection from "@/components/services/CtaSection.astro";
import PlusCardShowcase from "@/components/services/PlusCardShowcase.astro";
import JsonLd from "@/components/structured-data/JsonLd.astro";

// Import Assets
import websiteCreationImage from "@/assets/pages/jasa-pembuatan-website.webp";
import testimonialLogo from "@/assets/client-logo/maistroaudio.svg"; // Impor tanpa ?url
import testimonialLogoUrl from "@/assets/client-logo/maistroaudio.svg?url"; // Impor URL untuk JSON-LD
import ctaImagePlaceholder from "@/assets/pages/jasa-pembuatan-website.webp";

const metaDescription = "Jasa pembuatan website profesional (WordPress, Astro, Next.js). Solusi end-to-end: strategi, desain, development, optimasi Core Web Vitals & maintenance.";

// OG Image dan Alt Text
const ogImage = "/og-jasa-test/jasa-pembuatan-website.webp";
const ogImageAlt = "Jasa Pembuatan Website Premium - WordPress, Astro & Next.js";

// JSON-LD untuk halaman jasa
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Jasa Pembuatan Website Premium",
  "description": metaDescription,
  "provider": {
    "@type": "Organization",
    "name": "Harun Studio",
    "url": "https://harunstudio.com"
  },
  "serviceType": "Website Development",
  "offers": {
    "@type": "Offer",
    "priceSpecification": {
      "@type": "PriceSpecification",
      "minPrice": "5000000",
      "priceCurrency": "IDR"
    }
  },
  "areaServed": {
    "@type": "Country",
    "name": "Indonesia"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "5.0",
    "reviewCount": "3",
    "bestRating": "5",
    "worstRating": "1",
    "itemReviewed": {
      "@type": "Product",
      "name": "Jasa Pembuatan Website Modern",
      "image": "https://harunstudio.com/assets/pages/jasa-pembuatan-website.webp",
      "description": metaDescription,
      "brand": {
        "@type": "Brand",
        "name": "Harun Studio"
      },
      "sku": "01",
      "offers": {
        "@type": "AggregateOffer",
        "priceCurrency": "IDR",
        "lowPrice": "5000000",
        "highPrice": "25000000",
        "offerCount": 2
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5.0",
        "bestRating": "5",
        "worstRating": "1",
        "reviewCount": "3"
      }
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://harunstudio.com/jasa/pembuatan-website"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Layanan Website",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "WordPress Website",
          "description": "Website dengan CMS WordPress yang kuat dan fleksibel"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Astro Website",
          "description": "Website statis performa tinggi dengan Astro"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Next.js Web App",
          "description": "Aplikasi web kompleks dan dinamis dengan Next.js"
        }
      }
    ]
  },
  "hasFAQPage": {
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Platform/teknologi apa yang digunakan?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Kami menggunakan beberapa teknologi tergantung kebutuhan spesifik proyek: WordPress untuk website yang memerlukan CMS kuat dan fleksibel; Astro untuk website statis performa tinggi; dan Next.js untuk aplikasi web yang lebih kompleks dan dinamis. Kami akan merekomendasikan teknologi terbaik sesuai kebutuhan bisnis Anda."
        }
      },
      {
        "@type": "Question",
        "name": "Berapa lama proses pembuatan website?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Timeframe bervariasi berdasarkan kompleksitas proyek. Website landing page sederhana dapat selesai dalam 2-3 minggu, website bisnis umumnya 4-6 minggu, dan proyek kompleks dengan fitur custom bisa memerlukan 8-12 minggu. Kami akan memberikan timeline spesifik setelah memahami kebutuhan proyek Anda."
        }
      },
      {
        "@type": "Question",
        "name": "Bagaimana dengan performa website dan Core Web Vitals?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Kami sangat mengutamakan performa. Semua website kami dioptimasi untuk mencapai skor Core Web Vitals yang tinggi dengan waktu loading < 2 detik. Praktik seperti lazy loading, optimasi gambar, minifikasi aset, dan penggunaan CDN adalah standar dalam proses development kami."
        }
      },
      {
        "@type": "Question",
        "name": "Apakah termasuk layanan SEO?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ya, semua website kami dilengkapi dengan fondasi SEO teknis yang solid, termasuk struktur data terstruktur, metadata optimal, URL semantik, dan implementasi standar SEO on-page. Untuk kebutuhan SEO yang lebih komprehensif, kami juga menawarkan paket SEO terpisah."
        }
      },
      {
        "@type": "Question",
        "name": "Apa saja layanan maintenance yang tersedia?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Kami menawarkan beberapa paket maintenance bulanan yang mencakup update keamanan, backup otomatis, pemantauan uptime, optimasi performa berkelanjutan, dan jam dukungan teknis. Kami juga bisa menyusun paket maintenance custom sesuai kebutuhan spesifik Anda."
        }
      },
      {
        "@type": "Question",
        "name": "Bagaimana dengan revisi setelah desain?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Proses kami meliputi 2 ronde revisi besar untuk desain dan 1 ronde revisi minor setelah development. Jika diperlukan revisi tambahan, kami bisa mendiskusikan biaya tambahan yang wajar."
        }
      },
      {
        "@type": "Question",
        "name": "Apakah sudah termasuk domain dan hosting?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Domain dan hosting tidak termasuk dalam harga dasar, tapi kami bisa merekomendasikan dan membantu setup hosting yang optimal untuk teknologi website Anda. Kami juga menawarkan paket hosting managed khusus yang dioptimasi untuk performa."
        }
      },
      {
        "@type": "Question",
        "name": "Apakah website yang dibuat responsif untuk mobile?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Absolut! Semua website kami mengadopsi pendekatan mobile-first dan dioptimasi untuk tampil sempurna di semua ukuran layar, dari smartphone hingga desktop."
        }
      }
    ]
  }
};

// --- DATA KONTEN UNTUK HALAMAN PEMBUATAN WEBSITE ---
// Updated untuk mencakup semua teknologi dan layanan

// Data untuk Hero Section
const heroData = {
  title: "Jasa Pembuatan Website Modern",
  subtitle: "Solusi website all-in-one dari strategi hingga maintenance. Kami mengembangkan website performa tinggi dengan teknologi terbaru sesuai kebutuhan bisnis Anda.",
  primaryButtonText: "Diskusikan Kebutuhan Anda",
  primaryButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20bertanya%20tentang%20layanan%20pembuatan%20website%20di%20Harun%20Studio.",
  secondaryButtonText: "Pelajari Lebih Lanjut",
  secondaryButtonLink: "#business-focus",
  urgencyBadgeText: "⚡ September: 1 Slot Tersisa! Booking Segera"
};

// Data untuk Business Focus Section
const businessFocusData = {
  title: "Website Berperforma Tinggi untuk Bisnis Anda",
  subtitle: "Kami tidak hanya membangun website, tapi menghadirkan aset digital strategis yang memperkuat brand, meningkatkan konversi, dan memberikan pengalaman optimal kepada pengunjung.",
  listItems: [
    "Teknologi modern (WordPress, Astro, Next.js)",
    "Optimasi Core Web Vitals & SEO",
    "Desain UX/UI yang conversion-focused",
    "Support & maintenance berkualitas"
  ],
  buttonText: "Pelajari Manfaatnya",
  buttonLink: "#features",
  imageSrc: websiteCreationImage,
  imageAlt: "Modern Website Development Technologies",
  showImageShadow: false,
  id: "business-focus"
};

// Data untuk Benefits Section (Updated)
const benefitsData = {
  id: "features",
  title: "Layanan Website Komprehensif End-to-End",
  description: "Dari konsep awal hingga website live, kami menyediakan semua yang Anda butuhkan:",
  benefits: [
    { title: "Strategi & Copywriting", description: "Riset kompetitor, analisis target market, dan pengembangan konten yang menjual.", icon: "lucide:lightbulb", bullets: ["Brand & market research", "Site architecture planning", "Content strategy", "Conversion copywriting"] },
    { title: "Design & UX", description: "Desain user-centered yang memadukan estetika dengan conversion rate optimization.", icon: "lucide:palette", bullets: ["UI/UX research & design", "Wireframing & prototyping", "Mobile-first approach", "Accessible design"] },
    { title: "Development Modern", description: "Pengembangan dengan teknologi terkini sesuai kebutuhan bisnis dan skala proyek Anda.", icon: "lucide:code-2", bullets: ["WordPress (custom themes)", "Astro (performa optimal)", "Next.js (aplikasi kompleks)", "Integrasi API & sistem"] },
    { title: "Optimasi Performa", description: "Website cepat dengan skor Core Web Vitals tinggi untuk pengalaman pengguna terbaik.", icon: "lucide:bar-chart-2", bullets: ["Core Web Vitals optimization", "Load time < 2 detik", "Lazy loading & caching", "Image optimization"] },
    { title: "SEO & Analytics", description: "Fondasi SEO solid dan setup analytics untuk visibilitas dan pengukuran performa.", icon: "lucide:search", bullets: ["On-page SEO", "Structured data", "Google Analytics & Tag Manager", "Search Console setup"] },
    { title: "Maintenance & Support", description: "Dukungan berkelanjutan untuk menjaga website Anda aman, terbarui, dan optimal.", icon: "lucide:shield", bullets: ["Security updates", "Backup reguler", "Performance monitoring", "Technical support"] }
  ],
  showPlusFeatures: false,
  plusFeaturesTitle: "Kami Partner, Bukan Sekadar Vendor",
  plusFeaturesDescription: "Yang membedakan Harun Studio dari jasa pembuatan website lainnya:",
  plusFeatures: [
    { title: "Partner Jangka Panjang", description: "Kami memposisikan diri sebagai partner bisnis Anda, bukan sekadar vendor yang menyelesaikan tugas lalu menghilang. Kami percaya keberhasilan website Anda adalah keberhasilan kami juga.", icon: "lucide:handshake", bullets: ["Langsung dikerjakan pemilik", "Tidak ada subkontrak", "Komunikasi yang jelas", "Dukungan jangka panjang"] },
    { title: "Performa Tanpa Kompromi", description: "Website Anda dijamin mendapatkan score performa di atas 95 untuk mobile dan desktop di PageSpeed Insights dan score 100 untuk SEO—jauh di atas standar industri.", icon: "lucide:gauge", bullets: ["PageSpeed score 95+", "SEO score 100", "Core Web Vitals optimal", "Loading < 2 detik"] },
    { title: "One-Stop Solution", description: "Semua aspek website Anda ditangani oleh satu tim yang sama, dari desain hingga implementasi, menjamin konsistensi dan kualitas tinggi di setiap tahapnya.", icon: "lucide:check-circle", bullets: ["UI/UX yang conversion-focused", "Full-stack development", "Infrastruktur & deployment", "Support & maintenance"] }
  ],
  showTestimonial: true,
  testimonialQuote: "\"Seperti rekan kerja yang kompeten dan dapat diandalkan.\"",
  testimonialText: "\"Pengalaman layanan jasa pembuatan website dengan Harun Studio sangat memuaskan. Seperti rekan kerja yang kompeten dan dapat diandalkan, enak untuk diajak diskusi, dan memiliki etos yang baik dan profesional. Terima Kasih Harun Studio, sangat merekomendasikan Harun Studio untuk teman-teman yang ingin membuat website.\"",
  testimonialLogoSrc: testimonialLogo,
  testimonialLogoAlt: "Maistro Audio",
  testimonialAuthor: "Maikel Immanuel, MaistroAudio.com (Google Review)"
};

// Data untuk Process Section (Updated)
const processData = {
  showCta: true,
  ctaBadge: "MULAI HARI INI",
  ctaTitle: "Bangun Website Modern, Cepat & Siap Scale Up",
  ctaSubtitle: "Konsultasi gratis, strategi custom, dan pengembangan website dengan teknologi WordPress, Astro, atau Next.js.",
  ctaPriceText: null, // Atau isi dengan harga jika ingin menampilkan
  ctaButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20memulai%20proyek%20pembuatan%20website%20di%20Harun%20Studio.",
  ctaButtonText: "Konsultasi Gratis",
  ctaFootnote: "Tanpa biaya konsultasi. Proposal & timeline custom untuk setiap klien.",
  processTitle: "Bagaimana Kami Mewujudkan Website Impian Anda",
  processSubtitle: "Proses terstruktur dan kolaboratif untuk hasil optimal yang sesuai kebutuhan dan tujuan bisnis Anda.",
  steps: [
    { number: 1, title: "Discovery & Strategi", description: "Memahami bisnis, goals, target audience, dan kebutuhan teknis website Anda.", icon: "lucide:compass", details: "Kami menciptakan blueprint strategis sebagai fondasi proyek yang solid.", detailTags: [{icon: 'lucide:target', text: 'Goal Setting'}, {icon: 'lucide:users', text: 'Audience Analysis'}] },

    { number: 2, title: "UX Research & Design", description: "Merancang alur pengguna dan visual yang memprioritaskan konversi dan pengalaman optimal.", icon: "lucide:layout-dashboard", details: "Anda melihat dan menyetujui desain sebelum masuk ke tahap development.", detailTags: [{icon: 'lucide:pencil', text: 'Wireframing'}, {icon: 'lucide:palette', text: 'Visual Design'}] },

    { number: 3, title: "Development & Content", description: "Mengembangkan website dengan teknologi yang tepat (WordPress/Astro/Next.js) dan mengimplementasikan konten.", icon: "lucide:code", details: "Pembangunan website dengan kode bersih, modular, dan teroptimasi.", detailTags: [{icon: 'lucide:git-branch', text: 'Version Control'}, {icon: 'lucide:file-text', text: 'Content Integration'}] },

    { number: 4, title: "Testing & Optimization", description: "QA menyeluruh, optimasi kecepatan, dan penyesuaian untuk performa maksimal.", icon: "lucide:gauge", details: "Memastikan website berjalan sempurna di semua perangkat dengan kecepatan optimal.", detailTags: [{icon: 'lucide:smartphone', text: 'Device Testing'}, {icon: 'lucide:zap', text: 'Speed Optimization'}] },

    { number: 5, title: "Launch & Post-Launch", description: "Deployment website dan monitoring pasca-launch untuk memastikan semua berjalan sempurna.", icon: "lucide:rocket", details: "Transisi mulus ke website baru dengan dukungan teknis selama periode kritis.", detailTags: [{icon: 'lucide:server', text: 'Deployment'}, {icon: 'lucide:activity', text: 'Monitoring'}] }
  ]
};

// Data untuk FAQ Section (Updated)
const faqData = {
  title: "Pertanyaan Umum Seputar Jasa Website Kami",
  subtitle: "Informasi penting yang sering ditanyakan klien tentang layanan pembuatan website kami.",
  faqs: [
    { question: "Platform/teknologi apa yang digunakan?", answer: "Kami menggunakan beberapa teknologi tergantung kebutuhan spesifik proyek: WordPress untuk website yang memerlukan CMS kuat dan fleksibel; Astro untuk website statis performa tinggi; dan Next.js untuk aplikasi web yang lebih kompleks dan dinamis. Kami akan merekomendasikan teknologi terbaik sesuai kebutuhan bisnis Anda."},

    { question: "Berapa lama proses pembuatan website?", answer: "Timeframe bervariasi berdasarkan kompleksitas proyek. Website landing page sederhana dapat selesai dalam 2-3 minggu, website bisnis umumnya 4-6 minggu, dan proyek kompleks dengan fitur custom bisa memerlukan 8-12 minggu. Kami akan memberikan timeline spesifik setelah memahami kebutuhan proyek Anda."},

    { question: "Bagaimana dengan performa website dan Core Web Vitals?", answer: "Kami sangat mengutamakan performa. Semua website kami dioptimasi untuk mencapai skor Core Web Vitals yang tinggi dengan waktu loading < 2 detik. Praktik seperti lazy loading, optimasi gambar, minifikasi aset, dan penggunaan CDN adalah standar dalam proses development kami."},

    { question: "Apakah termasuk layanan SEO?", answer: "Ya, semua website kami dilengkapi dengan fondasi SEO teknis yang solid, termasuk struktur data terstruktur, metadata optimal, URL semantik, dan implementasi standar SEO on-page. Untuk kebutuhan SEO yang lebih komprehensif, kami juga menawarkan paket SEO terpisah."},

    { question: "Apa saja layanan maintenance yang tersedia?", answer: "Kami menawarkan beberapa paket maintenance bulanan yang mencakup update keamanan, backup otomatis, pemantauan uptime, optimasi performa berkelanjutan, dan jam dukungan teknis. Kami juga bisa menyusun paket maintenance custom sesuai kebutuhan spesifik Anda."},

    { question: "Bagaimana dengan revisi setelah desain?", answer: "Proses kami meliputi 2 ronde revisi besar untuk desain dan 1 ronde revisi minor setelah development. Jika diperlukan revisi tambahan, kami bisa mendiskusikan biaya tambahan yang wajar."},

    { question: "Apakah sudah termasuk domain dan hosting?", answer: "Domain dan hosting tidak termasuk dalam harga dasar, tapi kami bisa merekomendasikan dan membantu setup hosting yang optimal untuk teknologi website Anda. Kami juga menawarkan paket hosting managed khusus yang dioptimasi untuk performa."},

    { question: "Apakah website yang dibuat responsif untuk mobile?", answer: "Absolut! Semua website kami mengadopsi pendekatan mobile-first dan dioptimasi untuk tampil sempurna di semua ukuran layar, dari smartphone hingga desktop."}
  ],
  contactText: "Butuh informasi lebih lanjut?",
  contactButtonText: "Hubungi Tim Kami",
  contactButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20konsultasi%20tentang%20layanan%20pembuatan%20website%20di%20Harun%20Studio.",
};

// Data untuk CTA Section (Updated)
const ctaData = {
  urgencyBadgeText: "⚡ Sept: 1 Slot Tersisa! Booking Segera",
  title: "Wujudkan Website Performa Tinggi <br class=\"hidden md:block\" /> untuk Bisnis Anda",
  subtitle: "Konsultasikan kebutuhan website Anda dan dapatkan proposal custom dengan estimasi biaya lengkap.",
  secondaryButtonText: "Hubungi Kami",
  secondaryButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20membuat%20website%20baru%20bersama%20Harun%20Studio.",
  imageSrc: ctaImagePlaceholder,
  imageAlt: "Website Development Consultation",
  showImage: true
};
---

<Layout
  title="Jasa Pembuatan Website Profesional"
  description={metaDescription}
  ogImage={ogImage}
  ogImageAlt={ogImageAlt}
  ogType="service"
  jsonLd={jsonLd}
>
  <HeroSection {...heroData} />
  <BusinessFocusSection {...businessFocusData} />
  <BenefitsSection {...benefitsData} showPlusFeatures={false} />
  
  {/* Plus Features dengan desain baru */}
  <div class="py-12 md:py-16 lg:py-20">
    <PlusCardShowcase 
      title={benefitsData.plusFeaturesTitle}
      description={benefitsData.plusFeaturesDescription}
      features={benefitsData.plusFeatures}
    />
  </div>
  
  <ProcessSection {...processData} />
  <FaqSection {...faqData} />
  <CtaSection {...ctaData} />
</Layout>
