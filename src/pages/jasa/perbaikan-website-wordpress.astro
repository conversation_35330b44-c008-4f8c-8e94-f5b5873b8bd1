---
import Layout from "@/layouts/Layout.astro";
import QuickHero from "@/components/quick-services/QuickHero.astro";
import BenefitsSection from "@/components/services/BenefitsSection.astro";
import PlusCardShowcase from "@/components/services/PlusCardShowcase.astro";
import QuickProcess from "@/components/quick-services/QuickProcess.astro";
import QuickFAQ from "@/components/quick-services/QuickFAQ.astro";
import QuickCTA from "@/components/quick-services/QuickCTA.astro";
import CtaSimpleCentered from "@/components/quick-services/CtaSimpleCentered.astro";
import { Icon } from 'astro-icon/components';
import perbaikanWebsiteImage from '@/assets/pages/jasa-perbaikan-website.webp';
import koranNtbLogo from '@/assets/client-logo/logo-koran-ntb.jpg';

const metaDescription = "Jasa perbaikan website WordPress profesional. Atasi error kritis < 30 menit, tanpa downtime. Bayar setelah puas dengan garansi 30 hari. Respons cepat!";

// Placeholder OG Image - Ganti dengan path yang sesuai jika ada
const ogImage = "/src/assets/pages/jasa-perbaikan-website.webp"; 
const ogImageAlt = "Jasa Perbaikan Website WordPress Cepat dan Terpercaya";

// JSON-LD untuk halaman jasa
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Jasa Perbaikan Website WordPress Profesional",
  "description": metaDescription,
  "provider": {
    "@type": "Organization",
    "name": "Harun Studio",
    "url": "https://harunstudio.com"
  },
  "serviceType": "Website Repair",
  "offers": {
    "@type": "Offer",
    "priceSpecification": {
        "@type": "PriceSpecification",
        "minPrice": "250000",
        "priceCurrency": "IDR"
    }
  },
  "areaServed": {
    "@type": "Country",
    "name": "Indonesia"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "5.0",
    "bestRating": "5",
    "worstRating": "1",
    "reviewCount": "2",
    "itemReviewed": {
      "@type": "Product",
      "name": "Jasa Perbaikan Website WordPress Profesional",
      "image": "https://harunstudio.com/assets/pages/jasa-perbaikan-website.webp",
      "description": "Layanan perbaikan website WordPress profesional. Atasi error kritis dalam < 30 menit, tanpa downtime. Bayar setelah puas dengan garansi 30 hari. Respons cepat!",
      "brand": {
        "@type": "Brand",
        "name": "Harun Studio"
      },
      "sku": "03",
      "offers": {
        "@type": "Offer",
        "price": "250000",
        "priceCurrency": "IDR",
        "availability": "https://schema.org/InStock",
        "priceValidUntil": "2025-12-31"
      }
    }
  },
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://harunstudio.com/jasa/perbaikan-website-wordpress"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Layanan Perbaikan Website",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Perbaikan Error Kritis",
          "description": "Perbaikan cepat untuk error 5xx, white screen of death, database connection error"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Perbaikan Plugin/Tema",
          "description": "Perbaikan masalah plugin dan tema WordPress yang konflik atau error"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Optimasi Performa",
          "description": "Perbaikan dan optimasi kecepatan loading website WordPress"
        }
      }
    ]
  },
  "hasFAQPage": {
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Berapa lama waktu yang dibutuhkan untuk memperbaiki sebuah website?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Sebagian besar perbaikan ringan hingga menengah dapat kami selesaikan dalam waktu 15-30 menit saja. Untuk masalah yang lebih kompleks seperti kerusakan database besar atau infeksi malware yang parah, mungkin membutuhkan waktu 1-3 jam. Kami selalu memberikan estimasi waktu yang akurat sebelum memulai perbaikan, dan Anda akan mendapatkan update progres secara berkala."
        }
      },
      {
        "@type": "Question",
        "name": "Bagaimana jika Anda tidak dapat memperbaiki masalah website saya?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Kami menerapkan kebijakan \"No Fix, No Pay\" yang berarti jika kami tidak mampu memperbaiki website Anda, Anda tidak perlu membayar apa pun. Meski sangat jarang terjadi (tingkat keberhasilan kami 99%), jika setelah semua upaya kami masih belum bisa memperbaiki, kami akan memberikan diagnosis lengkap tentang masalahnya dan merekomendasikan alternatif solusi yang mungkin diperlukan."
        }
      },
      {
        "@type": "Question",
        "name": "Bagaimana dengan keamanan data dan konten website saya selama proses perbaikan?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Keamanan data adalah prioritas utama kami. Sebelum memulai perbaikan apa pun, kami selalu membuat backup lengkap dari website Anda sebagai langkah pengamanan. Selama proses perbaikan, kami mengikuti protokol keamanan ketat dan hanya melakukan perubahan yang diperlukan. Kami juga dapat menandatangani NDA jika diperlukan untuk informasi yang sangat sensitif."
        }
      },
      {
        "@type": "Question",
        "name": "Website saya terinfeksi malware atau diretas, bisakah Anda memperbaikinya?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ya, kami memiliki layanan khusus untuk pembersihan malware dan pemulihan website yang diretas. Namun, untuk kasus infeksi malware yang parah atau serangan hack yang kompleks, kami akan merekomendasikan Layanan Hapus Malware kami yang lebih komprehensif. Layanan ini tidak hanya membersihkan malware, tetapi juga memperkuat keamanan website untuk mencegah serangan di masa depan."
        }
      },
      {
        "@type": "Question",
        "name": "Informasi apa yang perlu saya siapkan untuk memulai proses perbaikan?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Untuk memulai proses perbaikan, sebaiknya siapkan informasi berikut: (1) Deskripsi detail masalah yang Anda alami, termasuk kapan mulai terjadi, (2) URL website dan halaman yang bermasalah, (3) Kredensial akses WordPress admin, (4) Akses hosting/cPanel jika diperlukan. Jangan khawatir jika Anda tidak yakin tentang beberapa detail teknis - kami akan membantu Anda mengidentifikasi apa yang dibutuhkan selama konsultasi awal."
        }
      },
      {
        "@type": "Question",
        "name": "Apakah Anda menawarkan layanan maintenance berkelanjutan untuk mencegah masalah di masa depan?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ya, kami menawarkan paket Website Maintenance WordPress bulanan yang komprehensif. Paket ini mencakup pemantauan 24/7, update rutin, backup otomatis, peningkatan keamanan, dan perbaikan error tanpa batas. Ini adalah pilihan ideal untuk bisnis yang ingin fokus pada operasional tanpa khawatir tentang masalah teknis website. Klien perbaikan mendapatkan diskon khusus untuk paket maintenance kami."
        }
      }
    ]
  }
};

// Data untuk QuickHero - Copywriting yang lebih powerful
const heroData = {
  title: "Jasa Perbaikan Website WordPress",
  subtitle: "Website error? Kehilangan pelanggan? Kena malware judi? Ada halaman aneh di Google Search? Kami perbaiki dahulu, Anda bayar jika sudah puas.",
  icon: "lucide:tool", 
  timeframe: "Waktu perbaikan: Rata-rata 15-30 menit",
  primaryButtonText: "Perbaiki Website Sekarang",
  primaryButtonLink: "https://wa.me/6281291274023?text=Halo%20Harun%20Studio%2C%20saya%20butuh%20bantuan%20untuk%20memperbaiki%20website%20WordPress%20saya.",
  secondaryButtonText: "Lihat Keunggulan",
  secondaryButtonLink: "#keunggulan",
};

// Data untuk BenefitsSection (Keunggulan & Harga) - Versi yang ditingkatkan
const benefitsData = {
  id: "keunggulan",
  title: "Mengapa Memilih Harun Studio untuk Perbaikan Website WordPress Anda?",
  description: "Solusi cepat, efektif, dan terpercaya untuk berbagai masalah website WordPress Anda. Kami fokus pada hasil, kecepatan, dan kepuasan klien.",
  price: "Mulai Rp250.000", 
  pricePeriod: "per perbaikan",
  benefits: [
    {
      title: "Respons & Perbaikan Ultra Cepat",
      description: "Respons dalam waktu kurang dari 7 menit dan sebagian besar perbaikan selesai dalam 15-30 menit, meminimalkan downtime dan kerugian bisnis Anda.",
      icon: "lucide:zap",
      bullets: ["Respons < 7 menit", "Perbaikan < 30 menit", "Prioritas penanganan", "Solusi instan"]
    },
    {
      title: "Spesialis WordPress 9+ Tahun",
      description: "Tim kami adalah spesialis WordPress dengan pengalaman 9+ tahun yang menangani ribuan kasus, menjamin solusi tepat untuk setiap masalah.",
      icon: "lucide:award",
      bullets: ["Lebih dari 9 tahun pengalaman", "Spesialis WordPress sejati", "Pengetahuan teknis mendalam", "Ribuan website diperbaiki"]
    },
     {
      title: "Keamanan Data Terjamin",
      description: "Kami selalu membuat backup sebelum memulai perbaikan dan mengikuti protokol keamanan ketat untuk melindungi data dan konten website Anda.",
      icon: "lucide:shield-check",
      bullets: ["Full backup otomatis", "Protokol keamanan ketat", "Zero data loss", "Proses transparan & aman"]
    },
  ],
  // Plus features akan ditampilkan menggunakan komponen PlusCardShowcase terpisah
  showPlusFeatures: false, // Nonaktifkan fitur plus bawaan BenefitsSection
  showTestimonial: true, 
  testimonialQuote: "\"Website jadi lancar kembali. Perasaan legah.\"",
  testimonialText: "\"Baru pertama dapat orang yang tepat bisa atasi serangan DDoS hanya dalam beberapa jam saja. Website jadi lancar kembali. Perasaan legah. Terimakasih Harun Studio\"",
  testimonialLogoSrc: koranNtbLogo,
  testimonialLogoAlt: "Koran NTB Logo",
  testimonialAuthor: "Satria Zr, koranntb.com (Google Review)" 
};

// Data untuk QuickProcess - Lebih terstruktur dan persuasif
const processData = {
  title: "Proses Perbaikan Website yang Transparan & Efisien",
  subtitle: "Kami menjamin proses perbaikan website yang cepat, efektif, dan tanpa kerumitan melalui 3 langkah sederhana:",
  steps: [
    {
      title: "Kontak & Konsultasi",
      description: "Hubungi kami via WhatsApp, jelaskan masalah yang Anda alami secara detail, dan dapatkan respons cepat dalam waktu < 7 menit.",
      icon: "lucide:message-circle",
      bullets: ["Jelaskan masalah website", "Respons < 7 menit", "Konsultasi awal gratis"],
      subtext: "Langkah awal yang mudah untuk mengatasi masalah website Anda."
    },
    {
      title: "Diagnosa & Solusi",
      description: "Tim ahli kami akan menganalisis masalah dan memberikan estimasi biaya, waktu, dan solusi yang tepat sebelum memulai perbaikan.",
      icon: "lucide:search",
      bullets: ["Analisis mendalam", "Estimasi transparan", "Solusi terukur"],
      subtext: "Diagnosa akurat untuk solusi yang tepat sasaran."
    },
    {
      title: "Perbaikan & Verifikasi",
      description: "Setelah persetujuan, kami segera memperbaiki masalah. Anda cukup bersantai sambil kami bekerja, lalu verifikasi hasilnya bersama.",
      icon: "lucide:check-circle",
      bullets: ["Perbaikan cepat", "Pengujian menyeluruh", "Verifikasi bersama"],
      subtext: "Perbaikan efisien dengan hasil yang terverifikasi."
    }
  ],
  cta: {
    text: "Mulai Perbaikan Sekarang",
    url: "https://wa.me/6281291274023?text=Halo%2C%20saya%20tertarik%20dengan%20layanan%20perbaikan%20website%20di%20Harun%20Studio."
  }
};

// Data untuk Jenis Perbaikan (Menggunakan CtaSimpleCentered yang diperbarui)
const repairTypesData = {
    title: "Masalah Website WordPress yang Kami Tangani",
    subtitle: "Dari error kritis hingga masalah performa dan kustomisasi, tim ahli kami siap mengatasi berbagai masalah:",
    buttonText: "Konsultasi Gratis",
    buttonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20konsultasi%20tentang%20masalah%20website%20saya.",
    icon: "lucide:help-circle",
    backgroundColor: "brand-light",
    showBadge: true,
    badgeText: "SOLUSI UNTUK SEGALA MASALAH"
};

// Data untuk QuickFAQ - Diperbarui untuk menangani kekhawatiran umum
const faqData = {
  title: "Pertanyaan Umum tentang Layanan Perbaikan Website",
  subtitle: "Temukan jawaban untuk pertanyaan-pertanyaan yang sering diajukan seputar layanan perbaikan website WordPress kami.",
  faqs: [
    {
      question: "Berapa lama waktu yang dibutuhkan untuk memperbaiki sebuah website?",
      answer: "Sebagian besar perbaikan ringan hingga menengah dapat kami selesaikan dalam waktu 15-30 menit saja. Untuk masalah yang lebih kompleks seperti kerusakan database besar atau infeksi malware yang parah, mungkin membutuhkan waktu 1-3 jam. Kami selalu memberikan estimasi waktu yang akurat sebelum memulai perbaikan, dan Anda akan mendapatkan update progres secara berkala."
    },
    {
      question: "Bagaimana jika Anda tidak dapat memperbaiki masalah website saya?",
      answer: "Kami menerapkan kebijakan \"No Fix, No Pay\" yang berarti jika kami tidak mampu memperbaiki website Anda, Anda tidak perlu membayar apa pun. Meski sangat jarang terjadi (tingkat keberhasilan kami 99%), jika setelah semua upaya kami masih belum bisa memperbaiki, kami akan memberikan diagnosis lengkap tentang masalahnya dan merekomendasikan alternatif solusi yang mungkin diperlukan."
    },
    {
      question: "Bagaimana dengan keamanan data dan konten website saya selama proses perbaikan?",
      answer: "Keamanan data adalah prioritas utama kami. Sebelum memulai perbaikan apa pun, kami selalu membuat backup lengkap dari website Anda sebagai langkah pengamanan. Selama proses perbaikan, kami mengikuti protokol keamanan ketat dan hanya melakukan perubahan yang diperlukan. Kami juga dapat menandatangani NDA jika diperlukan untuk informasi yang sangat sensitif."
    },
    {
      question: "Website saya terinfeksi malware atau diretas, bisakah Anda memperbaikinya?",
      answer: "Ya, kami memiliki layanan khusus untuk pembersihan malware dan pemulihan website yang diretas. Namun, untuk kasus infeksi malware yang parah atau serangan hack yang kompleks, kami akan merekomendasikan Layanan Hapus Malware kami yang lebih komprehensif. Layanan ini tidak hanya membersihkan malware, tetapi juga memperkuat keamanan website untuk mencegah serangan di masa depan."
    },
    {
      question: "Informasi apa yang perlu saya siapkan untuk memulai proses perbaikan?",
      answer: "Untuk memulai proses perbaikan, sebaiknya siapkan informasi berikut: (1) Deskripsi detail masalah yang Anda alami, termasuk kapan mulai terjadi, (2) URL website dan halaman yang bermasalah, (3) Kredensial akses WordPress admin, (4) Akses hosting/cPanel jika diperlukan. Jangan khawatir jika Anda tidak yakin tentang beberapa detail teknis - kami akan membantu Anda mengidentifikasi apa yang dibutuhkan selama konsultasi awal."
    },
    {
      question: "Apakah Anda menawarkan layanan maintenance berkelanjutan untuk mencegah masalah di masa depan?",
      answer: "Ya, kami menawarkan paket Website Maintenance WordPress bulanan yang komprehensif. Paket ini mencakup pemantauan 24/7, update rutin, backup otomatis, peningkatan keamanan, dan perbaikan error tanpa batas. Ini adalah pilihan ideal untuk bisnis yang ingin fokus pada operasional tanpa khawatir tentang masalah teknis website. Klien perbaikan mendapatkan diskon khusus untuk paket maintenance kami."
    }
  ],
  contactText: "Punya masalah website yang perlu segera ditangani?",
  contactButtonText: "Dapatkan Bantuan Segera",
  contactButtonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20membutuhkan%20bantuan%20segera%20untuk%20memperbaiki%20website%20saya."
};


// Data untuk QuickCTA - Lebih persuasif
const ctaData = {
  badge: "PERBAIKAN WEBSITE PROFESIONAL",
  title: "Website Bermasalah? Atasi dalam 30 Menit!",
  subtitle: "Jangan biarkan error website merugikan bisnis Anda lebih lama. Kmi siap memperbaiki dengan cepat, aman, dan terjamin hasilnya. Bayar setelah puas dengan garansi 30 hari penuh. Mulai dari Rp250.000 per perbaikan.",
  buttonText: "Perbaiki Website Anda Sekarang",
  buttonLink: "https://wa.me/6281291274023?text=Halo%2C%20saya%20ingin%20memperbaiki%20website%20WordPress%20saya%20yang%20bermasalah.",
  showImage: true,
  imageSrc: perbaikanWebsiteImage, // Gunakan variabel impor
  imageAlt: "Jasa Perbaikan Website WordPress"
};
---

<Layout 
  title="Jasa Perbaikan Website (Cepat & Terpercaya)" 
  description={metaDescription}
  ogImage={ogImage}
  ogImageAlt={ogImageAlt}
  ogType="service"
  jsonLd={jsonLd}
>
  <QuickHero {...heroData} />
  <BenefitsSection {...benefitsData} />

  {/* Plus Features Section */}
  <PlusCardShowcase 
    title="Layanan Nilai Tambah Eksklusif"
    description="Dapatkan keuntungan tambahan eksklusif dengan layanan perbaikan kami:"
    features={[
      {
        title: "Pay After Fix Guarantee",
        description: "Anda hanya membayar setelah website Anda benar-benar berfungsi normal kembali. Jika kami tidak berhasil memperbaiki, Anda tidak perlu membayar.",
        icon: "lucide:wallet",
        bullets: ["Bayar setelah puas", "No fix - no pay", "Tanpa risiko finansial", "Transparansi biaya"]
      },
      {
        title: "Garansi & After-Sales Premium",
        description: "Dapatkan garansi perbaikan 30 hari dan dukungan pasca-perbaikan. Jika masalah yang sama muncul kembali, kami tangani gratis!",
        icon: "lucide:shield-check",
        bullets: ["Garansi 30 hari", "Dukungan pasca-perbaikan", "Penanganan ulang gratis", "Panduan pencegahan"]
      },
      {
        title: "Diskon & Benefit Khusus",
        description: "Dapatkan diskon khusus untuk layanan lanjutan, optimasi, atau maintenance rutin setelah perbaikan awal.",
        icon: "lucide:percent",
        bullets: ["Diskon hingga 20%", "Paket maintenance bulanan", "Harga spesial klien tetap", "Prioritas dukungan"]
      }
    ]}
  />

  {/* Bagian Jenis Perbaikan - Menggunakan CtaSimpleCentered yang diperbarui */}
  <section class="py-16">
    <CtaSimpleCentered {...repairTypesData}>
      <Fragment slot="after_subtitle">
        <div class="mt-8 max-w-4xl mx-auto grid grid-cols-2 sm:grid-cols-3 gap-4 text-left">
          {[ 
            "Error Kritis / 5xx",
            "White Screen of Death",
            "Database Connection Error",
            "Tidak Bisa Login",
            "Masalah WooCommerce",
            "Konfigurasi Plugin/Tema",
            "Masalah Contact Form",
            "Setup/Masalah DNS",
            "Error PHP / CSS",
            "Tweak CSS Ringan",
            "Bantuan Custom Snippet",
            "Perbaikan Umum Lainnya"
          ].map(item => (
            <div class="flex items-center gap-2 p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-[var(--color-brand-100)] transition-all duration-300">
              <Icon name="lucide:check-circle" class="w-5 h-5 text-green-500 flex-shrink-0" />
              <span class="text-sm text-gray-700">{item}</span>
            </div>
          ))}
        </div>
        <div class="mt-6 text-sm text-gray-500">
          <p>Dan banyak lagi masalah WordPress lainnya. Jika Anda tidak yakin, konsultasikan saja dengan kami!</p>
        </div>
      </Fragment>
    </CtaSimpleCentered>
  </section>

  <QuickProcess {...processData} />
  <QuickFAQ {...faqData} />
  <QuickCTA {...ctaData} />
</Layout> 