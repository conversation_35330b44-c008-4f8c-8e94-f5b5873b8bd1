// 1. Import utilities from `astro:content`
import { z, defineCollection } from 'astro:content';

// 2. Define your collection(s)
const blogCollection = defineCollection({
  type: 'content',
  schema: ({ image }) => z.object({
    draft: z.boolean().optional().default(false),
    title: z.string(),
    snippet: z.string(),
    image: z.object({
      src: image(),
      alt: z.string(),
    }),
    publishDate: z.string().transform((str) => new Date(str)),
    author: z.string(),
    category: z.string(),
    tags: z.array(z.string()),
  }),
});

const caseStudyCollection = defineCollection({
  type: 'content',
  schema: () => z.object({
    draft: z.boolean().optional().default(false),
    title: z.string(),
    description: z.string(),
    snippet: z.string(),

    // Client information
    client: z.object({
      name: z.string(),
      logo: z.string().optional(),
      url: z.string().optional(),
    }),

    // Project details
    industry: z.string(),
    location: z.string().optional(),
    timeline: z.object({
      start: z.string(),
      end: z.string(),
    }),
    services: z.array(z.string()),

    // Key Performance Indicators
    kpis: z.array(z.object({
      label: z.string(),
      before: z.union([z.string(), z.number()]),
      after: z.union([z.string(), z.number()]),
      unit: z.string().optional(),
      improvement: z.string().optional(), // e.g., "+75.47%"
    })),

    // Before/After comparisons
    beforeAfter: z.array(z.object({
      title: z.string(),
      beforeImg: z.string(),
      afterImg: z.string(),
      caption: z.string().optional(),
    })).optional(),

    // Technology stack
    techStack: z.array(z.string()),

    // Testimonial
    testimonial: z.object({
      quote: z.string(),
      author: z.string(),
      role: z.string(),
      company: z.string().optional(),
    }).optional(),

    // SEO and metadata
    image: z.object({
      src: z.string(),
      alt: z.string(),
    }),
    publishDate: z.string().transform((str) => new Date(str)),
    author: z.string(),
    tags: z.array(z.string()),

    // Related services
    relatedServices: z.array(z.object({
      title: z.string(),
      slug: z.string(),
    })).optional(),
  }),
});

// 3. Export a single `collections` object to register your collection(s)
//    This key should match your collection directory name in "src/content"
export const collections = {
  'blog': blogCollection,
  'case-studies': caseStudyCollection,
};