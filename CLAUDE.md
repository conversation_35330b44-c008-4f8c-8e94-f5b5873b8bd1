# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is Harun Studio's business website built with Astro and TailwindCSS. Harun Studio is a Indonesian web development agency specializing in WordPress optimization, modern web technologies, and business-focused web solutions. The company positions itself as a technology partner for business owners, offering everything from quick fixes to complete digital transformations.

## Development Commands

Use `pnpm` as the package manager (specified in package.json):

```bash
# Development server
pnpm dev  # or npm run dev

# Build for production  
pnpm build  # or npm run build

# Preview production build
pnpm preview  # or npm run preview
```

Development server runs on `http://localhost:4321` (Astro default).

## Architecture

### Core Structure
- **Astro Static Site**: Modern static site generator with component-based architecture
- **Content Collections**: Blog posts managed via Astro's content collections API in `src/content/blog/`
- **Component Library**: Modular components organized by feature in `src/components/`
- **Layout System**: Two main layouts - `Layout.astro` (general) and `BlogLayout.astro` (blog-specific)

### Key Directories
- `src/components/`: Organized by feature (home/, services/, ui/, navbar/, etc.)
- `src/pages/`: File-based routing including service pages in `jasa/` subdirectory
- `src/content/`: Blog content with MDX support and type-safe schema
- `src/assets/`: Images organized by type (blog/, client-logo/, images/, pages/)
- `src/styles/`: Global CSS and component-specific styles

### Content Management
- Blog posts use MDX format with frontmatter schema defined in `src/content/config.ts`
- Schema includes: title, snippet, image (src/alt), publishDate, author, category, tags
- Images are processed through Astro's image optimization

### Styling & UI
- TailwindCSS with Vite plugin integration
- Plus Jakarta Sans variable font
- Custom UI components in `src/components/ui/`
- Global styles in `src/styles/global.css`

### SEO & Integrations
- Built-in SEO with astro-seo package
- Automatic sitemap generation
- Robots.txt integration
- OpenGraph image handling in layouts
- Structured data support via JsonLd component

### Service Pages Architecture
Service pages follow a consistent structure with reusable components:
- `HeroSection.astro` - Main hero with service description
- `ProcessSection.astro` - Step-by-step process
- `BenefitsSection.astro` - Service benefits
- `FaqSection.astro` - Common questions
- `CtaSection.astro` - Call-to-action

Path aliases configured: `@/*` → `src/*`, `~/*` → `./*`

## Development Notes

- Uses file-based routing with Astro pages
- Component props follow TypeScript interfaces
- Images stored in `src/assets/` are automatically optimized
- Blog content uses content collections for type safety
- WhatsApp integration via FloatingWhatsApp component

## Content Strategy & Layanan

### Blog Content Portfolio

The blog contains 13 comprehensive articles across several key categories:

**Content Pillars:**
- **WordPress Expertise**: Maintenance guides, security, performance optimization, and best practices
- **Modern Web Technologies**: Astro, Next.js, AI-powered development, and technology comparisons
- **Case Studies**: Real client transformations with measurable results (Win Equipment, Muslimadani.id, PT Zumatic)
- **Business ROI**: Focus on practical business outcomes and decision-making frameworks

**Content Types:**
- **Tutorial & Guides**: Practical implementation guides for WordPress maintenance, hosting selection, and security
- **Case Studies**: Before/after transformations with concrete metrics and ROI analysis
- **Technology Comparisons**: WordPress vs Astro, hosting types, with decision frameworks for business owners
- **Industry Insights**: AI development trends, modern web technologies, business impact analysis

**Target Audience:**
- Business owners with WordPress websites seeking optimization
- UKM and medium enterprises needing performance improvements
- Decision makers evaluating web technology choices
- E-commerce store owners looking for conversions

### Services Offered

Harun Studio offers 10 core services across three categories:

**Development Services (Project-based):**
- **Pembuatan Website** (Rp 5-25 juta): Custom WordPress/Astro development
- **Migrasi WordPress ke Astro** (Rp 7 juta): Performance-focused technology transformation
- **Konversi ke Blocks WordPress** (Rp 5 juta): Page builder to Gutenberg modernization
- **Redesign Website** (Rp 8+ juta): Complete website transformation
- **SEO On-page** (Rp 2.5 juta): Technical SEO audit and optimization

**Quick Fix Services (Immediate):**
- **Migrasi Website** (Rp 250k): Hosting/server migration
- **Perbaikan Website** (Rp 250k+): Emergency fixes and troubleshooting
- **Hapus Malware WordPress** (Rp 750k): Security remediation and cleanup

**Ongoing Services (Monthly):**
- **WordPress Hosting** (Rp 250k-500k/month): Managed hosting with personal support
- **Maintenance Website** (Rp 1-1.75 juta/month): Complete website care and updates

**Key Value Propositions:**
- "Partner, bukan vendor" approach with personal WhatsApp support
- Performance-first methodology (target <2s loading, 95+ Core Web Vitals)
- Business outcome focus rather than technical complexity
- "No panel, no hassle" hosting philosophy for busy business owners
- Zero downtime migrations and quick response times (<10 minutes support, <30 minutes fixes)

### Writing Guidelines for New Articles

When creating new blog content, follow these established patterns:

**Article Structure:**
- Problem-focused opening addressing specific pain points
- Data-driven content with real metrics and case studies
- Solution-oriented approach with actionable implementation steps
- Clear call-to-action linking to relevant services

**Content Style:**
- Conversational but professional tone
- Story-driven narratives using real client experiences
- Educational value before sales messaging
- Honest assessment with balanced pros/cons
- Technical depth with practical implementation details

**Visual Elements:**
- Before/after screenshots for transformations
- Performance charts and metrics
- Tables for comparisons and reference
- Code snippets for technical articles

**SEO Strategy:**
- Long-tail keywords targeting specific problems
- Comprehensive topic coverage
- Strategic internal linking to service pages
- Business-focused keywords (ROI, efficiency, productivity)